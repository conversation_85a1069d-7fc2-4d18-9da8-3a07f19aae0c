import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON>To<PERSON><PERSON>, Join<PERSON>olumn, Index, ManyToOne } from "typeorm";
import { AuditableBaseEntity } from "./base-entity";
import { IsUUID } from "class-validator";
import { QATaskSubmissions } from "./qa-task-submissions.entity";
import { QATaskSubmissionHistory } from "./qa-task-submission-history.entity";

@Entity()
@Index(["submissionId", "submissionHistoryId"])
export class QATaskSubmissionMarking extends AuditableBaseEntity {
  @Column({
    name: "points",
    type: "float",
    nullable: false
  })
  points: number;

  @Column({
    name: "submission_feedback",
    type: "text",
    nullable: true
  })
  submissionFeedback?: string;

  @Column({
    name: "task_remarks",
    type: "text",
    nullable: true
  })
  taskRemarks?: string;



  @Column({
    name: "submission_id",
    type: "uuid",
    nullable: false
  })
  @IsUUID()
  submissionId: string;

  @Column({
    name: "submission_history_id",
    type: "uuid",
    nullable: false
  })
  @IsUUID()
  submissionHistoryId: string;

  @ManyToOne(() => QATaskSubmissions, submission => submission.submissionHistory, {nullable: true})
  @JoinColumn({ name: "submission" })
  submission: QATaskSubmissions;

  @OneToOne('QATaskSubmissionMarking', (marking: QATaskSubmissionMarking) => marking.submissionHistory)
  //@OneToOne(() => QATaskSubmissionHistory, submissionHistory => submissionHistory.submissionMark)
  @JoinColumn({ name: "submission_mark_id" })
  submissionHistory: QATaskSubmissionHistory;
}