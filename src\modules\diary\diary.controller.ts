import { Controller, Get, Post, Body, Param, Patch, Delete, UseGuards, UseInterceptors, UploadedFile, Req, Query, BadRequestException, ValidationPipe, Res, NotFoundException } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiTags, ApiQuery, ApiConsumes } from '@nestjs/swagger';
import { DiaryService } from './diary.service';
import { DiaryLikeService } from './diary-like.service';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { StudentGuard } from '../../common/guards/student.guard';
import { SubscriptionFeatureGuard } from '../../common/guards/subscription-feature.guard';
import { RequireFeature } from '../../common/decorators/require-feature.decorator';
import { FeatureType } from '../../database/entities/plan-feature.entity';
import { StudentOnly } from '../../common/decorators/student-only.decorator';
import { Public } from '../../common/decorators/public-api.decorator';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  CreateDiaryEntryDto,
  UpdateDiaryEntryDto,
  ShareDiaryEntryDto,
  DiaryEntryResponseDto,
  DiarySkinResponseDto,
  DiaryShareResponseDto,
  StudentTutorListResponseDto,
  DiaryAwardsResponseDto,
  DiaryPeriodAwardsResponseDto,
  DiaryEntryFilterDto,
  CreateStudentDiarySkinDto,
  UpdateStudentDiarySkinDto,
  UpdateTutorGreetingDto,
  SubmitDiaryEntryDto,
  UpdateDefaultSkinDto,
  UpdateTodaysDiarySkinDto
} from '../../database/models/diary.dto';
import { AddThanksMessageDto } from '../../database/models/diary-thanks.dto';
import { ApiResponse } from 'src/common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiErrorResponse, ApiOkResponseWithPagedListType } from 'src/common/decorators/api-response.decorator';
import { PagedListDto } from 'src/common/models/paged-list.dto';
import { PaginationDto } from 'src/common/models/pagination.dto';
import { DiaryLikeResponseDto, DiaryLikeCountResponseDto } from '../../database/models/diary-like.dto';
import { LikerType } from '../../database/entities/diary-entry-like.entity';
import { StudentTutorGuard } from '../../common/guards/student-tutor.guard';
import { FileRegistryService } from 'src/common/services/file-registry.service';
import { FileEntityType } from 'src/common/enums/file-entity-type.enum';
import { CurrentUserService } from '../../common/services/current-user.service';
import * as path from 'path';
import * as fs from 'fs';
import { Response } from 'express';

@ApiTags('diary')
@Controller('diary')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class DiaryController {
  constructor(
    private readonly diaryService: DiaryService,
    private readonly diaryLikeService: DiaryLikeService,
    private readonly fileRegistryService: FileRegistryService,
    private readonly currentUserService: CurrentUserService
  ) {}

  @Get('skins')
  @ApiOperation({
    summary: 'Get all diary skins',
    description: 'Get a list of all available diary skins/templates.'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination'
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page'
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by'
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)'
  })
  @ApiOkResponseWithPagedListType(DiarySkinResponseDto, 'List of diary skins retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(500, 'Internal server error')
  async getDiarySkins(@Req() req: any, @Query() paginationDto?: PaginationDto): Promise<ApiResponse<PagedListDto<DiarySkinResponseDto>>> {
    const studentId = req.user.id;
    const skins = await this.diaryService.getDiarySkins(false, studentId, paginationDto); // Include student's own skins
    return ApiResponse.success(skins, 'Diary skins retrieved successfully');
  }

  @Post('skins')
  @UseInterceptors(FileInterceptor('previewImage'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Create a new student diary skin',
    description: 'Creates a new diary skin/template for the student. Only accessible by the student who owns it.'
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          example: 'My Custom Skin',
          description: 'Name of the diary skin'
        },
        description: {
          type: 'string',
          example: 'My personal theme with custom colors',
          description: 'Description of the diary skin'
        },
        templateContent: {
          type: 'string',
          example: '<div class="student-diary-template">{{content}}</div>',
          description: 'HTML template content for the skin'
        },
        isActive: {
          type: 'boolean',
          example: true,
          description: 'Whether the skin is active and available for use'
        },
        previewImage: {
          type: 'string',
          format: 'binary',
          description: 'Preview image file for the diary skin (JPEG, PNG, or GIF)'
        }
      },
      required: ['name', 'description', 'templateContent']
    }
  })
  @ApiOkResponseWithType(DiarySkinResponseDto, 'Student diary skin created successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(409, 'Diary skin with the same name already exists')
  async createStudentDiarySkin(@Req() req: any, @Body() createStudentDiarySkinDto: CreateStudentDiarySkinDto, @UploadedFile() previewImage?: any): Promise<ApiResponse<DiarySkinResponseDto>> {
    if (!previewImage) {
      throw new BadRequestException('Preview image is required');
    }

    const studentId = req.user.id;
    const skin = await this.diaryService.createStudentDiarySkin(studentId, createStudentDiarySkinDto, previewImage);
    return ApiResponse.success(skin, 'Student diary skin created successfully', 201);
  }

  @Get('skins/:id')
  @ApiOperation({
    summary: 'Get a diary skin by ID',
    description: 'Get details of a specific diary skin by ID. For student skins, only the owner can access it.'
  })
  @ApiOkResponseWithType(DiarySkinResponseDto, 'Diary skin retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Diary skin not found')
  async getDiarySkinById(@Param('id') id: string, @Req() req: any): Promise<ApiResponse<DiarySkinResponseDto>> {
    const studentId = req.user.id;

    try {
      // First try to get it as a student skin
      const skin = await this.diaryService.getStudentDiarySkinById(id, studentId);
      return ApiResponse.success(skin, 'Student diary skin retrieved successfully');
    } catch (error) {
      if (error instanceof NotFoundException) {
        // If not found as a student skin, try to get it as a global skin
        try {
          const skin = await this.diaryService.getDiarySkinById(id);
          return ApiResponse.success(skin, 'Diary skin retrieved successfully');
        } catch (innerError) {
          throw innerError;
        }
      }
      throw error;
    }
  }

  @Patch('skins/:id')
  @UseInterceptors(FileInterceptor('previewImage'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Update a student diary skin',
    description: 'Update an existing student diary skin. Only accessible by the student who owns it.'
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          example: 'My Updated Custom Skin',
          description: 'Name of the diary skin'
        },
        description: {
          type: 'string',
          example: 'My updated personal theme with custom colors',
          description: 'Description of the diary skin'
        },
        templateContent: {
          type: 'string',
          example: '<div class="updated-student-diary-template">{{content}}</div>',
          description: 'HTML template content for the skin'
        },
        isActive: {
          type: 'boolean',
          example: false,
          description: 'Whether the skin is active and available for use'
        },
        previewImage: {
          type: 'string',
          format: 'binary',
          description: 'Preview image file for the diary skin (JPEG, PNG, or GIF)'
        }
      }
    }
  })
  @ApiOkResponseWithType(DiarySkinResponseDto, 'Student diary skin updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Student diary skin not found')
  @ApiErrorResponse(409, 'Diary skin with the same name already exists')
  async updateStudentDiarySkin(
    @Param('id') id: string,
    @Req() req: any,
    @Body() updateStudentDiarySkinDto: UpdateStudentDiarySkinDto,
    @UploadedFile() previewImage?: any
  ): Promise<ApiResponse<DiarySkinResponseDto>> {
    const studentId = req.user.id;
    const skin = await this.diaryService.updateStudentDiarySkin(id, studentId, updateStudentDiarySkinDto, previewImage);
    return ApiResponse.success(skin, 'Student diary skin updated successfully');
  }

  @Delete('skins/:id')
  @ApiOperation({
    summary: 'Delete a student diary skin',
    description: 'Delete a student diary skin. Only accessible by the student who owns it.'
  })
  @ApiOkResponseWithType(Object, 'Student diary skin deleted successfully')
  @ApiErrorResponse(400, 'Cannot delete skin as it is being used by diary entries')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Student diary skin not found')
  async deleteStudentDiarySkin(@Param('id') id: string, @Req() req: any): Promise<ApiResponse<null>> {
    const studentId = req.user.id;
    await this.diaryService.deleteStudentDiarySkin(id, studentId);
    return ApiResponse.success(null, 'Student diary skin deleted successfully');
  }

  // Diary-specific operations (student only)
  @UseGuards(StudentGuard, SubscriptionFeatureGuard)
  @RequireFeature(FeatureType.HEC_USER_DIARY)
  @Post('entries')
  @ApiOperation({
    summary: 'Create a diary entry for a specific date',
    description:
      "Create a diary entry for a specific date (past or future). Use this endpoint when you need to create entries for dates other than today. For today's entry, use the GET /diary/entries/today endpoint instead. If an entry already exists for the specified date, returns the existing entry."
  })
  @ApiBody({
    type: CreateDiaryEntryDto,
    description: 'Diary entry initialization data (entryDate is required)'
  })
  @ApiOkResponseWithType(DiaryEntryResponseDto, 'Diary entry created or retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(403, 'Forbidden - Only students can create diary entries')
  @ApiErrorResponse(500, 'Internal server error')
  async createDiaryEntry(
    @Req() req: any,
    @Body(new ValidationPipe({
      whitelist: false,
      forbidNonWhitelisted: false,
      transform: true
    })) createDiaryEntryDto: CreateDiaryEntryDto
  ): Promise<ApiResponse<DiaryEntryResponseDto>> {
    const userId = req.user.sub;

    // Ensure entryDate is provided
    if (!createDiaryEntryDto.entryDate) {
      throw new BadRequestException('entryDate is required for creating diary entries for specific dates');
    }

    const entry = await this.diaryService.createDiaryEntry(userId, createDiaryEntryDto);

    // Determine if this was a new entry or an existing one    // Determine status code based on whether the entry was just created or already existed
    const statusCode = entry.createdAt.toISOString() === entry.updatedAt.toISOString() ? 201 : 200;
    const message = statusCode === 201 ? `Diary entry for ${createDiaryEntryDto.entryDate} created successfully` : `Existing diary entry for ${createDiaryEntryDto.entryDate} retrieved successfully`;

    return ApiResponse.success(entry, message, statusCode);
  }

  // Diary-specific operations (student only)
  @UseGuards(StudentGuard, SubscriptionFeatureGuard)
  @RequireFeature(FeatureType.HEC_USER_DIARY)
  @Get('entries')
  @ApiOperation({
    summary: 'Get all diary entries for the current user',
    description: 'Retrieve all diary entries created by the current user.'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination'
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page'
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by'
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)'
  })  @ApiOkResponseWithPagedListType(DiaryEntryResponseDto, 'Diary entries retrieved successfully')
  @ApiErrorResponse(400, 'Unable to retrieve diary entries')
  @ApiErrorResponse(404, 'User not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getDiaryEntries(@Req() req: any, @Query() paginationDto?: PaginationDto): Promise<ApiResponse<PagedListDto<DiaryEntryResponseDto>>> {
    try {
      const userId = req.user.sub;
      // Always pass false for includeFriends to ensure only own entries are returned
      const entries = await this.diaryService.getStudentDiaryEntries(userId, paginationDto, false);
      return ApiResponse.success(entries, 'Diary entries retrieved successfully');
    } catch (error) {
      // Let NestJS handle the exception and transform it into the appropriate HTTP response
      throw error;
    }
  }

  @Get('friends/entries')
  @ApiOperation({
    summary: 'Get diary entries from friends',
    description: 'Retrieve diary entries from friends that have been shared with the current user.'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination'
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page'
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by'
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)'
  })
  @ApiOkResponseWithPagedListType(DiaryEntryResponseDto, 'Friend diary entries retrieved successfully')
  @ApiErrorResponse(400, 'Unable to retrieve friend diary entries')
  @ApiErrorResponse(404, 'User not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getFriendDiaryEntries(@Req() req: any, @Query() paginationDto?: PaginationDto): Promise<ApiResponse<PagedListDto<DiaryEntryResponseDto>>> {
    try {
      const userId = req.user.sub;
      const entries = await this.diaryService.getStudentDiaryEntries(userId, paginationDto, true);
      return ApiResponse.success(entries, 'Friend diary entries retrieved successfully');
    } catch (error) {
      // Let NestJS handle the exception and transform it into the appropriate HTTP response
      throw error;
    }
  }

  @UseGuards(StudentGuard, SubscriptionFeatureGuard)
  @RequireFeature(FeatureType.HEC_USER_DIARY)
  @Get('entries/today')
  @ApiOperation({
    summary: "Get or create today's diary entry",
    description: 'Get the diary entry for today for the currently authenticated student. If no entry exists, a new one will be created with minimal initialization.'
  })
  @ApiOkResponseWithType(DiaryEntryResponseDto, "Today's diary entry retrieved or created successfully")
  @ApiErrorResponse(400, "Unable to retrieve or create today's diary entry")
  @ApiErrorResponse(404, 'User not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getTodaysDiaryEntry(@Req() req: any): Promise<ApiResponse<DiaryEntryResponseDto>> {
    try {
      const userId = req.user.sub;
      const entry = await this.diaryService.getTodaysDiaryEntry(userId);

      // Determine if this was a new entry or an existing one based on creation time
      const isNewEntry = entry.createdAt && new Date(entry.createdAt).getTime() > Date.now() - 5000;
      const message = isNewEntry ? "Today's diary entry created successfully" : "Today's diary entry retrieved successfully";

      return ApiResponse.success(entry, message);
    } catch (error) {
      // Let NestJS handle the exception and transform it into the appropriate HTTP response
      throw error;
    }
  }

  @Get('entries/:id')
  @ApiOperation({
    summary: 'Get a specific diary entry',
    description: 'Retrieve a specific diary entry by its ID.'
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary entry to retrieve',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  @ApiOkResponseWithType(DiaryEntryResponseDto, 'Diary entry retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Diary entry not found')
  @ApiErrorResponse(403, 'Forbidden - You do not have permission to view this diary entry')
  @ApiErrorResponse(500, 'Internal server error')
  async getDiaryEntry(@Req() req: any, @Param('id') id: string): Promise<ApiResponse<DiaryEntryResponseDto>> {
    const userId = req.user.sub;
    const entry = await this.diaryService.getDiaryEntry(id, userId);
    return ApiResponse.success(entry, 'Diary entry retrieved successfully');
  }

  @Patch('entries/:id')
  @ApiOperation({
    summary: 'Update a diary entry',
    description: 'Update an existing diary entry.'
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary entry to update',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  @ApiBody({
    type: UpdateDiaryEntryDto,
    description: 'Diary entry update data'
  })
  @ApiOkResponseWithType(DiaryEntryResponseDto, 'Diary entry updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Diary entry not found')
  @ApiErrorResponse(403, 'Forbidden - You do not have permission to update this diary entry or only students can update diary entries')
  @ApiErrorResponse(500, 'Internal server error')
  async updateDiaryEntry(
    @Req() req: any,
    @Param('id') id: string,
    @Body(new ValidationPipe({
      whitelist: false,
      forbidNonWhitelisted: false,
      transform: true
    })) updateDiaryEntryDto: UpdateDiaryEntryDto
  ): Promise<ApiResponse<DiaryEntryResponseDto>> {
    const userId = req.user.sub;
    const entry = await this.diaryService.updateDiaryEntry(id, userId, updateDiaryEntryDto);
    return ApiResponse.success(entry, 'Diary entry updated successfully');
  }

  @Post('entries/:id/submit')
  @ApiOperation({
    summary: 'Submit a diary entry for review',
    description: 'Submit a diary entry for review by a tutor. Requires complete entry data including title, content, and settings.'
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary entry to submit',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  @ApiBody({
    type: SubmitDiaryEntryDto,
    description: 'Complete diary entry data for submission'
  })
  @ApiOkResponseWithType(DiaryEntryResponseDto, 'Diary entry submitted successfully')
  @ApiErrorResponse(400, 'Invalid input or missing required fields')
  @ApiErrorResponse(404, 'Diary entry not found')
  @ApiErrorResponse(403, 'Forbidden - You do not have permission to submit this diary entry')
  @ApiErrorResponse(409, 'Conflict - This diary entry is not in the correct state for submission')
  @ApiErrorResponse(500, 'Internal server error')  async submitDiaryEntry(@Req() req: any, @Param('id') id: string, @Body() submitDiaryEntryDto: SubmitDiaryEntryDto): Promise<ApiResponse<DiaryEntryResponseDto>> {
    const userId = req.user.sub;
    const entry = await this.diaryService.submitDiaryEntry(id, userId, submitDiaryEntryDto);
    return ApiResponse.success(entry, 'Diary entry submitted successfully');
  }

  @Post('entries/:id/share')
  @ApiOperation({
    summary: 'Share a diary entry',
    description: 'Share a diary entry and generate a QR code. Makes the entry public.'
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary entry to share',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  @ApiBody({
    type: ShareDiaryEntryDto,
    description: 'Diary entry sharing data'
  })
  @ApiOkResponseWithType(DiaryShareResponseDto, 'Diary entry shared successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Diary entry not found')
  @ApiErrorResponse(403, 'Forbidden - You do not have permission to share this diary entry')
  @ApiErrorResponse(500, 'Internal server error')
  async shareDiaryEntry(@Req() req: any, @Param('id') id: string, @Body() shareDiaryEntryDto: ShareDiaryEntryDto): Promise<ApiResponse<DiaryShareResponseDto>> {
    const userId = req.user.sub;
    const result = await this.diaryService.shareDiaryEntry(id, userId, shareDiaryEntryDto);
    return ApiResponse.success(result, 'Diary entry shared successfully');
  }

  @Public()
  @Get('shared-entries')
  @ApiOperation({
    summary: 'Get all publicly shared diary entries',
    description: 'Retrieve all diary entries that have been publicly shared by any user.'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination'
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page'
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by'
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)'
  })
  @ApiOkResponseWithPagedListType(DiaryEntryResponseDto, 'Publicly shared diary entries retrieved successfully')
  @ApiErrorResponse(400, 'Unable to retrieve shared diary entries')
  @ApiErrorResponse(500, 'Internal server error')
  async getAllSharedDiaryEntries(
    @Query() paginationDto?: PaginationDto
  ): Promise<ApiResponse<PagedListDto<DiaryEntryResponseDto>>> {
    // Extract user ID if user is authenticated (for hasLiked calculation)
    // Since this is a @Public() endpoint, use CurrentUserService which is populated by CurrentUserMiddleware for all routes
    const userId = this.currentUserService.getCurrentUserId();
    const entries = await this.diaryService.getPubliclySharedEntries(paginationDto, userId);
    return ApiResponse.success(entries, 'Publicly shared diary entries retrieved successfully');
  }


  @Post('entries/:id/make-private')
  @ApiOperation({
    summary: 'Make a diary entry private',
    description: 'Make a diary entry private and deactivate all share links.'
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary entry to make private',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  @ApiOkResponseWithType(Object, 'Diary entry made private successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Diary entry not found')
  @ApiErrorResponse(403, 'Forbidden - You do not have permission to modify this diary entry')
  @ApiErrorResponse(500, 'Internal server error')
  async makeEntryPrivate(@Req() req: any, @Param('id') id: string): Promise<ApiResponse<any>> {
    const userId = req.user.sub;
    await this.diaryService.makeEntryPrivate(id, userId);
    return ApiResponse.success({ success: true }, 'Diary entry made private successfully');
  }

  @Get('tutors')
  @ApiOperation({
    summary: 'Get all tutors available for sharing',
    description: 'Get a list of all tutors that the student can share diary entries with.'
  })
  @ApiOkResponseWithType(StudentTutorListResponseDto, 'Tutors retrieved successfully')
  @ApiErrorResponse(404, 'User not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getStudentTutors(@Req() req: any): Promise<ApiResponse<StudentTutorListResponseDto>> {
    const userId = req.user.sub;
    const tutors = await this.diaryService.getStudentTutors(userId);
    return ApiResponse.success(tutors, 'Tutors retrieved successfully');
  }

  @Get('awards')
  @ApiOperation({
    summary: 'Get student awards',
    description: 'Get all awards earned by the current student.'
  })
  @ApiOkResponseWithType(DiaryAwardsResponseDto, 'Awards retrieved successfully')
  @ApiErrorResponse(404, 'User not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getStudentAwards(@Req() req: any): Promise<ApiResponse<DiaryAwardsResponseDto>> {
    const userId = req.user.sub;
    const awards = await this.diaryService.getStudentAwards(userId);
    return ApiResponse.success(awards, 'Awards retrieved successfully');
  }

  @Get('period-awards')
  @ApiOperation({
    summary: 'Get student period awards',
    description: 'Get all period awards (weekly/monthly) earned by the current student.'
  })
  @ApiOkResponseWithType(DiaryPeriodAwardsResponseDto, 'Period awards retrieved successfully')
  @ApiErrorResponse(404, 'User not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getStudentPeriodAwards(@Req() req: any): Promise<ApiResponse<DiaryPeriodAwardsResponseDto>> {
    const userId = req.user.sub;
    const awards = await this.diaryService.getStudentPeriodAwards(userId);
    return ApiResponse.success(awards, 'Period awards retrieved successfully');
  }

  @Post('greeting')
  @ApiOperation({
    summary: 'Set tutor greeting message',
    description: 'Set a greeting message for tutors. This is required before creating diary entries.'
  })
  @ApiBody({
    type: UpdateTutorGreetingDto,
    description: 'Greeting message for tutors'
  })
  @ApiOkResponseWithType(Object, 'Greeting message set successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'User not found')
  @ApiErrorResponse(500, 'Internal server error')
  async setTutorGreeting(@Req() req: any, @Body() updateTutorGreetingDto: UpdateTutorGreetingDto): Promise<ApiResponse<any>> {
    const userId = req.user.sub;
    await this.diaryService.updateTutorGreeting(userId, updateTutorGreetingDto.greeting);
    return ApiResponse.success({ success: true }, 'Greeting message set successfully');
  }

  @Post('entries/:id/thanks-message')
  @ApiOperation({
    summary: 'Add thanks message to a diary entry',
    description: 'Add a thanks message to a diary entry after it has been reviewed and confirmed by a tutor.'
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary entry',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  @ApiBody({
    type: AddThanksMessageDto,
    description: 'Thanks message data'
  })
  @ApiOkResponseWithType(DiaryEntryResponseDto, 'Thanks message added successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Diary entry not found')
  @ApiErrorResponse(403, 'Forbidden - You do not have permission to add a thanks message to this diary entry')
  @ApiErrorResponse(409, 'Conflict - This diary entry is not in the correct state for adding a thanks message')
  @ApiErrorResponse(500, 'Internal server error')
  async addThanksMessage(@Req() req: any, @Param('id') id: string, @Body() addThanksMessageDto: AddThanksMessageDto): Promise<ApiResponse<DiaryEntryResponseDto>> {
    const userId = req.user.sub;
    // Add the thanks message
    await this.diaryService.addThanksMessage(id, userId, addThanksMessageDto.thanksMessage);

    // Get the complete entry with all relations for the response
    const completeEntry = await this.diaryService.getDiaryEntry(id, userId);
    return ApiResponse.success(completeEntry, 'Thanks message added successfully');
  }

  @Get('filter')
  @ApiOperation({
    summary: 'Filter diary entries',
    description: 'Filter diary entries by date or subject.'
  })
  @ApiQuery({
    name: 'date',
    required: false,
    description: 'Filter by date (YYYY-MM-DD)',
    example: '2023-07-25'
  })
  @ApiQuery({
    name: 'subject',
    required: false,
    description: 'Filter by subject',
    example: 'Mathematics'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination'
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page'
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by'
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)'
  })
  @ApiOkResponseWithPagedListType(DiaryEntryResponseDto, 'Diary entries filtered successfully')
  @ApiErrorResponse(400, 'Invalid filter parameters')
  @ApiErrorResponse(404, 'User not found')
  @ApiErrorResponse(500, 'Internal server error')
  async filterDiaryEntries(
    @Req() req: any,
    @Query('date') date?: string,
    @Query('subject') subject?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'ASC' | 'DESC'
  ): Promise<ApiResponse<PagedListDto<DiaryEntryResponseDto>>> {
    const userId = req.user.sub;
    const filterDto = { date, subject };
    const paginationDto = { page, limit, sortBy, sortDirection };

    const entries = await this.diaryService.filterDiaryEntries(userId, filterDto, paginationDto);
    return ApiResponse.success(entries, 'Diary entries filtered successfully');
  }

  @Patch('default-skin')
  @ApiOperation({
    summary: 'Update default diary skin',
    description: 'Update the default diary skin for the currently authenticated student.'
  })
  @ApiBody({
    type: UpdateDefaultSkinDto,
    description: 'Default skin update data'
  })
  @ApiOkResponseWithType(Object, 'Default skin updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Diary skin not found')
  @ApiErrorResponse(500, 'Internal server error')
  async updateDefaultSkin(@Req() req: any, @Body() updateDefaultSkinDto: UpdateDefaultSkinDto): Promise<ApiResponse<any>> {
    const userId = req.user.sub;
    await this.diaryService.setDefaultDiarySkin(userId, updateDefaultSkinDto.skinId);
    return ApiResponse.success({ success: true }, 'Default skin updated successfully');
  }

  @Patch('today/skin')
  @ApiOperation({
    summary: "Update today's diary entry skin",
    description: "Update the skin of today's diary entry for the currently authenticated student."
  })
  @ApiBody({
    type: UpdateTodaysDiarySkinDto,
    description: "Today's diary skin update data"
  })
  @ApiOkResponseWithType(DiaryEntryResponseDto, "Today's diary skin updated successfully")
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Diary skin not found or no diary entry for today')
  @ApiErrorResponse(500, 'Internal server error')
  async updateTodaysDiarySkin(@Req() req: any, @Body() updateTodaysDiarySkinDto: UpdateTodaysDiarySkinDto): Promise<ApiResponse<DiaryEntryResponseDto>> {
    const userId = req.user.sub;
    const updatedEntry = await this.diaryService.updateTodaysDiarySkin(userId, updateTodaysDiarySkinDto.skinId);
    return ApiResponse.success(updatedEntry, "Today's diary skin updated successfully");
  }

  @Post('entries/:id/like')
  @UseGuards(StudentTutorGuard)
  @ApiOperation({
    summary: 'Like a diary entry',
    description: 'Adds a like to the specified diary entry (only available to students and tutors)'
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the diary entry to like',
    type: 'string'
  })
  @ApiOkResponseWithType(DiaryLikeResponseDto, 'Diary entry liked successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(403, 'Only students and tutors can like diary entries')
  @ApiErrorResponse(404, 'Diary entry not found')
  async likeDiaryEntry(
    @Req() req: any,
    @Param('id') id: string
  ): Promise<ApiResponse<DiaryLikeResponseDto>> {
    const like = await this.diaryLikeService.addLike(id, req.user.id, req.user.type);
    return ApiResponse.success(like, 'Diary entry liked successfully');
  }

  @Delete('entries/:id/like')
  @UseGuards(StudentTutorGuard)
  @ApiOperation({
    summary: 'Unlike a diary entry',
    description: 'Removes a like from the specified diary entry (only available to students and tutors)'
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the diary entry to unlike',
    type: 'string'
  })
  @ApiOkResponseWithType(DiaryLikeResponseDto, 'Diary entry unliked successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(403, 'Only students and tutors can unlike diary entries')
  @ApiErrorResponse(404, 'Diary entry not found')
  async unlikeDiaryEntry(
    @Req() req: any,
    @Param('id') id: string
  ): Promise<ApiResponse<DiaryLikeResponseDto>> {
    await this.diaryLikeService.removeLike(id, req.user.id);
    return ApiResponse.success(null, 'Diary entry unliked successfully');
  }

  @Get('entries/:id/likes')
  @UseGuards(StudentTutorGuard)
  @ApiOperation({
    summary: 'Get diary entry likes',
    description: 'Get the number of likes and like status for the specified diary entry'
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the diary entry',
    type: 'string'
  })
  @ApiOkResponseWithType(DiaryLikeCountResponseDto, 'Diary entry likes retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(403, 'Only students and tutors can view like details')
  @ApiErrorResponse(404, 'Diary entry not found')
  async getDiaryEntryLikes(
    @Req() req: any,
    @Param('id') id: string
  ): Promise<ApiResponse<DiaryLikeCountResponseDto>> {
    const [likeCount, hasLiked] = await Promise.all([
      this.diaryLikeService.getLikeCount(id),
      this.diaryLikeService.hasUserLiked(id, req.user.id)
    ]);
    return ApiResponse.success({ count: likeCount, hasLiked }, 'Diary entry likes retrieved successfully');
  }

  @Get('entries/:id/like-count')
  @ApiOperation({
    summary: 'Get diary entry like count',
    description: 'Get the number of likes for the specified diary entry'
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the diary entry',
    type: 'string'
  })
  @ApiOkResponseWithType(DiaryLikeCountResponseDto, 'Diary entry like count retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Diary entry not found')
  @Public()  // Make this endpoint public
  async getDiaryEntryLikeCount(
    @Param('id') id: string
  ): Promise<ApiResponse<DiaryLikeCountResponseDto>> {
    const count = await this.diaryLikeService.getLikeCount(id);
    return ApiResponse.success({ count, hasLiked: false }, 'Diary entry like count retrieved successfully');
  }

  @Get('entries/:id/qr')
  @ApiOperation({
    summary: 'Download QR code for a shared diary entry',
    description: 'Download the QR code image that was generated when the diary entry was shared.'
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary entry',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  @ApiOkResponseWithType(Object, 'QR code downloaded successfully')
  @ApiErrorResponse(404, 'QR code not found')
  @ApiErrorResponse(403, 'Forbidden - You do not have permission to access this QR code')
  @ApiErrorResponse(500, 'Internal server error')
  async downloadQrCode(@Param('id') id: string, @Req() req: any, @Res() res: Response): Promise<void> {
    const userId = req.user.id;

    // Get the QR code file from registry
    const qrFile = await this.fileRegistryService.getFile(FileEntityType.DIARY_QR, id);

    if (!qrFile) {
      throw new NotFoundException('QR code not found. The diary entry may not be shared yet.');
    }

    // Read file and send it as response
    const filePath = path.resolve('uploads', qrFile.filePath);
    if (!fs.existsSync(filePath)) {
      throw new NotFoundException('QR code file not found');
    }

    res.setHeader('Content-Type', qrFile.mimeType || 'image/png');
    res.setHeader('Content-Disposition', `attachment; filename=${path.basename(qrFile.filePath)}`);
    fs.createReadStream(filePath).pipe(res);
  }
}
