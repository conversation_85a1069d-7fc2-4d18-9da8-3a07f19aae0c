import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  Req,
  ParseUUIDPipe
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiBody
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../common/guards/jwt.guard';
import { StudentGuard } from '../../../common/guards/student.guard';
import { SubscriptionFeatureGuard } from '../../../common/guards/subscription-feature.guard';
import { RequireFeature } from '../../../common/decorators/require-feature.decorator';
import { FeatureType } from '../../../database/entities/plan-feature.entity';
import { ApiResponse } from '../../../common/dto/api-response.dto';
import {
  ApiOkResponseWithType,
  ApiOkResponseWithArrayType,
  ApiOkResponseWithPagedListType,
  ApiErrorResponse
} from '../../../common/decorators/api-response.decorator';
import { NovelTopicService } from '../services/novel-topic.service';
import { NovelEntryService } from '../services/novel-entry.service';
import { NovelSuggestionService } from '../services/novel-suggestion.service';
import { DiarySkinService } from '../../diary/diary-skin.service';
import {
  NovelTopicResponseDto,
  UpdateNovelEntryDto,
  SubmitNovelEntryDto,
  NovelEntryResponseDto,
  CreateNovelSuggestionDto,
  NovelSuggestionResponseDto,
  SetNovelSkinPreferenceDto,
  NovelSkinPreferenceResponseDto
} from '../../../database/models/novel.dto';
import { DiarySkinResponseDto } from '../../../database/models/diary.dto';
import { PaginationDto } from '../../../common/models/pagination.dto';
import { PagedListDto } from '../../../common/models/paged-list.dto';

@ApiTags('student-novel')
@ApiBearerAuth('JWT-auth')
@Controller('student/novel')
@UseGuards(JwtAuthGuard, StudentGuard, SubscriptionFeatureGuard)
@RequireFeature(FeatureType.ENGLISH_NOVEL)
export class StudentNovelController {
  constructor(
    private readonly novelTopicService: NovelTopicService,
    private readonly novelEntryService: NovelEntryService,
    private readonly novelSuggestionService: NovelSuggestionService,
    private readonly diarySkinService: DiarySkinService
  ) {}

  @Get('topics')
  @ApiOperation({ summary: 'Get active novel topics by category' })
  @ApiQuery({
    name: 'category',
    required: false,
    enum: ['monthly', 'quarterly'],
    description: 'Filter topics by category (monthly or quarterly)'
  })
  @ApiOkResponseWithArrayType(NovelTopicResponseDto, 'Retrieved novel topics')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async getTopics(
    @Query('category') category?: string
  ): Promise<ApiResponse<NovelTopicResponseDto[]>> {
    const result = await this.novelTopicService.getActiveTopicsByCategory(category);
    return ApiResponse.success(result, 'Retrieved novel topics');
  }

  @Get('topics/:id')
  @ApiOperation({ summary: 'Get a specific novel topic by ID' })
  @ApiParam({ name: 'id', description: 'Topic ID', type: String })
  @ApiOkResponseWithType(NovelTopicResponseDto, 'Retrieved novel topic successfully')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Topic not found')
  async getTopicById(
    @Param('id', ParseUUIDPipe) id: string
  ): Promise<ApiResponse<NovelTopicResponseDto>> {
    const result = await this.novelTopicService.getTopicById(id);
    return ApiResponse.success(result, 'Retrieved novel topic successfully');
  }



  @Put('entries/:id')
  @ApiOperation({ summary: 'Update a novel entry' })
  @ApiParam({ name: 'id', description: 'Entry ID', type: String })
  @ApiBody({
    type: UpdateNovelEntryDto,
    description: 'Novel entry update data',
    examples: {
      example1: {
        value: {
          content: 'Once upon a time, in a magical kingdom...',
          skinId: '123e4567-e89b-12d3-a456-************'
        }
      }
    }
  })
  @ApiOkResponseWithType(NovelEntryResponseDto, 'Novel entry updated successfully')
  @ApiErrorResponse(400, 'Invalid input data or entry cannot be updated')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Entry not found')
  async updateEntry(
    @Req() req: any,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateEntryDto: UpdateNovelEntryDto
  ): Promise<ApiResponse<NovelEntryResponseDto>> {
    const result = await this.novelEntryService.updateEntry(req.user.id, id, updateEntryDto);
    return ApiResponse.success(result, 'Novel entry updated successfully');
  }

  @Post('entries/submit')
  @ApiOperation({ summary: 'Submit a novel entry for review' })
  @ApiBody({
    type: SubmitNovelEntryDto,
    description: 'Novel entry submission data with optional content and skin updates',
    examples: {
      example1: {
        summary: 'Submit with content and skin update',
        value: {
          entryId: '123e4567-e89b-12d3-a456-************',
          content: 'Once upon a time, in a magical kingdom far away...',
          skinId: '123e4567-e89b-12d3-a456-************'
        }
      },
      example2: {
        summary: 'Submit without updates',
        value: {
          entryId: '123e4567-e89b-12d3-a456-************'
        }
      }
    }
  })
  @ApiOkResponseWithType(NovelEntryResponseDto, 'Novel entry submitted successfully')
  @ApiErrorResponse(400, 'Invalid input data or entry already reviewed')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Entry not found')
  async submitEntry(
    @Req() req: any,
    @Body() submitEntryDto: SubmitNovelEntryDto
  ): Promise<ApiResponse<NovelEntryResponseDto>> {
    const result = await this.novelEntryService.submitEntry(req.user.id, submitEntryDto);
    return ApiResponse.success(result, 'Novel entry submitted successfully');
  }

  @Get('entries')
  @ApiOperation({ summary: 'Get student novel entries' })
  @ApiQuery({
    name: 'category',
    required: false,
    enum: ['monthly', 'quarterly'],
    description: 'Filter entries by topic category (monthly or quarterly)'
  })
  @ApiOkResponseWithArrayType(NovelEntryResponseDto, 'Retrieved novel entries')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async getEntries(
    @Req() req: any,
    @Query('category') category?: string
  ): Promise<ApiResponse<NovelEntryResponseDto[]>> {
    const result = await this.novelEntryService.getStudentEntries(req.user.id, category);
    return ApiResponse.success(result, 'Retrieved novel entries');
  }

  @Get('entries/:id')
  @ApiOperation({ summary: 'Get a specific novel entry by ID' })
  @ApiParam({ name: 'id', description: 'Entry ID', type: String })
  @ApiOkResponseWithType(NovelEntryResponseDto, 'Retrieved novel entry successfully')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Entry not found')
  async getEntryById(
    @Req() req: any,
    @Param('id', ParseUUIDPipe) id: string
  ): Promise<ApiResponse<NovelEntryResponseDto>> {
    const result = await this.novelEntryService.getEntryById(id, req.user.id);
    return ApiResponse.success(result, 'Retrieved novel entry successfully');
  }

  @Get('entries/topic/:topicId')
  @ApiOperation({ summary: 'Get or create novel entry by topic ID' })
  @ApiParam({ name: 'topicId', description: 'Topic ID', type: String })
  @ApiOkResponseWithType(NovelEntryResponseDto, 'Retrieved or created novel entry successfully')
  @ApiErrorResponse(400, 'Invalid input data or missing default skin')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Topic not found or inactive')
  async getOrCreateEntryByTopicId(
    @Req() req: any,
    @Param('topicId', ParseUUIDPipe) topicId: string
  ): Promise<ApiResponse<NovelEntryResponseDto>> {
    const result = await this.novelEntryService.getOrCreateEntryByTopicId(req.user.id, topicId);
    return ApiResponse.success(result, 'Retrieved or created novel entry successfully');
  }

  @Post('suggestions')
  @ApiOperation({ summary: 'Create a novel topic suggestion' })
  @ApiBody({
    type: CreateNovelSuggestionDto,
    description: 'Novel suggestion creation data',
    examples: {
      example1: {
        value: {
          title: 'Fantasy Adventure',
          description: 'A story about magical creatures and their adventures in an enchanted forest'
        }
      }
    }
  })
  @ApiOkResponseWithType(NovelSuggestionResponseDto, 'Novel suggestion created successfully')
  @ApiErrorResponse(400, 'Invalid input data')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async createSuggestion(
    @Req() req: any,
    @Body() createSuggestionDto: CreateNovelSuggestionDto
  ): Promise<ApiResponse<NovelSuggestionResponseDto>> {
    const result = await this.novelSuggestionService.createSuggestion(req.user.id, createSuggestionDto);
    return ApiResponse.success(result, 'Novel suggestion created successfully', 201);
  }

  @Get('suggestions')
  @ApiOperation({ summary: 'Get student novel suggestions' })
  @ApiOkResponseWithArrayType(NovelSuggestionResponseDto, 'Retrieved novel suggestions')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async getSuggestions(
    @Req() req: any
  ): Promise<ApiResponse<NovelSuggestionResponseDto[]>> {
    const result = await this.novelSuggestionService.getStudentSuggestions(req.user.id);
    return ApiResponse.success(result, 'Retrieved novel suggestions');
  }

  @Post('skin-preference')
  @ApiOperation({ summary: 'Set default skin preference for novel module' })
  @ApiBody({
    type: SetNovelSkinPreferenceDto,
    description: 'Skin preference data',
    examples: {
      example1: {
        value: {
          defaultSkinId: '123e4567-e89b-12d3-a456-************'
        }
      }
    }
  })
  @ApiOkResponseWithType(NovelSkinPreferenceResponseDto, 'Skin preference set successfully')
  @ApiErrorResponse(400, 'Invalid input data')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async setDefaultSkin(
    @Req() req: any,
    @Body() setSkinDto: SetNovelSkinPreferenceDto
  ): Promise<ApiResponse<NovelSkinPreferenceResponseDto>> {
    const result = await this.novelEntryService.setDefaultSkin(req.user.id, setSkinDto);
    return ApiResponse.success(result, 'Skin preference set successfully');
  }

  @Get('skin-preference')
  @ApiOperation({ summary: 'Get default skin preference for novel module' })
  @ApiOkResponseWithType(NovelSkinPreferenceResponseDto, 'Retrieved skin preference')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Skin preference not found')
  async getDefaultSkin(
    @Req() req: any
  ): Promise<ApiResponse<NovelSkinPreferenceResponseDto | null>> {
    const result = await this.novelEntryService.getDefaultSkin(req.user.id);
    return ApiResponse.success(result, 'Retrieved skin preference');
  }

  @Get('skins')
  @ApiOperation({ summary: 'Get all available skins for novel entries' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination'
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page'
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by'
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)'
  })
  @ApiOkResponseWithPagedListType(DiarySkinResponseDto, 'Retrieved available skins')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async getSkins(
    @Req() req: any,
    @Query() paginationDto?: PaginationDto
  ): Promise<ApiResponse<PagedListDto<DiarySkinResponseDto>>> {
    const studentId = req.user.id;
    const result = await this.diarySkinService.getDiarySkins(false, studentId, paginationDto);
    return ApiResponse.success(result, 'Retrieved available skins');
  }
}
