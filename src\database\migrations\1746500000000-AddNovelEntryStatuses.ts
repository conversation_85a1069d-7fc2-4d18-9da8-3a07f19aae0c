import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddNovelEntryStatuses1746500000000 implements MigrationInterface {
  name = 'AddNovelEntryStatuses1746500000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if the table exists first
    const tableExists = await queryRunner.hasTable('novel_entry');
    if (!tableExists) {
      console.log('novel_entry table does not exist, skipping migration');
      return;
    }

    try {
      // Try to find and drop any existing check constraints on the status column
      const constraintQuery = `
        SELECT tc.constraint_name
        FROM information_schema.table_constraints tc
        JOIN information_schema.check_constraints cc ON tc.constraint_name = cc.constraint_name
        WHERE tc.table_name = 'novel_entry'
        AND tc.constraint_type = 'CHECK'
        AND cc.check_clause LIKE '%status%'
      `;

      const constraints = await queryRunner.query(constraintQuery);

      // Drop existing status check constraint if it exists
      for (const constraint of constraints) {
        const constraintName = constraint.constraint_name;
        console.log(`Dropping existing constraint: ${constraintName}`);
        await queryRunner.query(`
          ALTER TABLE "novel_entry"
          DROP CONSTRAINT "${constraintName}"
        `);
      }
    } catch (error) {
      console.log('Error dropping existing constraints:', error.message);
      // Continue with adding new constraint
    }

    // Add new check constraint with updated statuses
    await queryRunner.query(`
      ALTER TABLE "novel_entry"
      ADD CONSTRAINT "novel_entry_status_check"
      CHECK ("status" IN ('new', 'submitted', 'updated', 'correction_given', 'reviewed', 'under_review', 'confirmed'))
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Check if the table exists first
    const tableExists = await queryRunner.hasTable('novel_entry');
    if (!tableExists) {
      console.log('novel_entry table does not exist, skipping rollback');
      return;
    }

    // Drop the new constraint
    await queryRunner.query(`
      ALTER TABLE "novel_entry"
      DROP CONSTRAINT IF EXISTS "novel_entry_status_check"
    `);

    // Restore original constraint (this assumes the original constraint was dropped)
    await queryRunner.query(`
      ALTER TABLE "novel_entry"
      ADD CONSTRAINT "novel_entry_status_check_original"
      CHECK ("status" IN ('new', 'submitted', 'updated', 'reviewed', 'confirmed'))
    `);
  }
}
