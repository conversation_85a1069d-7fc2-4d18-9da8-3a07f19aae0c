import { Injectable, Logger, ForbiddenException } from '@nestjs/common';
import { FeatureType } from '../../database/entities/plan-feature.entity';
import { JwtPayload } from '../../modules/auth/interfaces/jwt-payload.interface';
import { UserType } from '../../database/entities/user.entity';

@Injectable()
export class SubscriptionService {
  private readonly logger = new Logger(SubscriptionService.name);

  /**
   * Validate feature access using JWT payload information
   * This is the main method used by the guard - simple and efficient
   * @param user JWT payload containing plan information
   * @param featureType Single feature type to validate
   * @param featureTypes Array of feature types (for any/all validation)
   * @param requireAll Whether all features are required (default: false)
   */
  validateFeatureAccess(
    user: JwtPayload,
    featureType?: FeatureType,
    featureTypes?: FeatureType[],
    requireAll: boolean = false
  ): void {
    console.log('🔍 SubscriptionService.validateFeatureAccess called:', {
      userId: user.id,
      userType: user.type,
      featureType,
      featureTypes,
      requireAll,
      activePlan: user.activePlan,
      planType: user.planType,
      planActive: user.planActive
    });

    // Only validate for students
    if (user.type !== UserType.STUDENT) {
      console.log('✅ Non-student user, skipping validation');
      return;
    }

    // Check if user has active subscription
    if (!user.activePlan || user.planActive === false) {
      console.log('❌ No active subscription found');
      throw new ForbiddenException('You need an active subscription to access this feature');
    }

    // If no specific feature is required, just check for active subscription
    if (!featureType && (!featureTypes || featureTypes.length === 0)) {
      console.log('✅ Active subscription found, no specific feature required');
      return;
    }

    // For now, we'll implement a simple mapping based on plan types
    // This can be enhanced later to use actual plan features from database
    let hasAccess = false;
    let featureName = '';

    if (featureType) {
      hasAccess = this.checkFeatureAccess(user, featureType);
      featureName = this.getFeatureName(featureType);
      console.log(`🔍 Single feature check: ${featureType} = ${hasAccess}`);
    } else if (featureTypes && Array.isArray(featureTypes) && featureTypes.length > 0) {
      if (requireAll) {
        hasAccess = featureTypes.every(ft => this.checkFeatureAccess(user, ft));
        featureName = featureTypes.map(ft => this.getFeatureName(ft)).join(', ');
        console.log(`🔍 ALL features check: ${featureTypes.join(', ')} = ${hasAccess}`);
      } else {
        hasAccess = featureTypes.some(ft => this.checkFeatureAccess(user, ft));
        featureName = featureTypes.map(ft => this.getFeatureName(ft)).join(' or ');
        console.log(`🔍 ANY features check: ${featureTypes.join(', ')} = ${hasAccess}`);
      }
    } else {
      console.log('⚠️ No valid feature type or feature types provided');
      console.log('Debug info:', { featureType, featureTypes, isArray: Array.isArray(featureTypes) });
    }

    console.log(`🎯 Final access decision: ${hasAccess} for feature(s): ${featureName}`);

    if (!hasAccess) {
      const message = requireAll
        ? `Your subscription plan does not include all required features: ${featureName}. Please upgrade your plan to access this resource.`
        : `Your subscription plan does not include the ${featureName} feature. Please upgrade your plan to access this resource.`;

      console.log(`❌ Access denied: ${message}`);
      this.logger.warn(`Feature access denied for user ${user.id}: ${message}`);
      throw new ForbiddenException(message);
    }

    console.log(`✅ Access granted for feature(s): ${featureName}`);
  }

  /**
   * Check if user has access to a specific feature based on actual plan features
   * Uses legacy features from JWT payload to determine access
   * @param user JWT payload
   * @param featureType Feature to check
   * @returns True if user has access
   */
  private checkFeatureAccess(user: JwtPayload, featureType: FeatureType): boolean {
    console.log('🔍 Checking feature access:', {
      featureType,
      userPlanType: user.planType,
      userActivePlan: user.activePlan
    });

    // For now, we need to check if the JWT contains plan features
    // Since the current JWT doesn't include legacyFeatures, we'll need to
    // implement a database lookup or enhance the JWT to include features

    // TODO: This should be replaced with actual feature checking from JWT or database
    // For now, let's implement the correct logic based on the seeding data

    const planType = user.planType?.toLowerCase();

    switch (featureType) {
      case FeatureType.HEC_USER_DIARY:
        // Diary available in all plans (starter, standard, pro, ultimate)
        return ['starter', 'standard', 'pro', 'ultimate'].includes(planType || '');

      case FeatureType.HEC_PLAY:
        // Play available in starter and above
        return ['standard', 'pro', 'ultimate'].includes(planType || '');

      case FeatureType.ENGLISH_QA_WRITING:
        // QA Writing available in standard and above
        return ['starter', 'standard', 'pro', 'ultimate'].includes(planType || '');

      case FeatureType.ENGLISH_ESSAY:
        // Essay available in ultimate plan only (based on seeding data)
        return ['pro', 'ultimate'].includes(planType || '');

      case FeatureType.ENGLISH_NOVEL:
        // Novel available in ultimate plan only (based on seeding data)
        return planType === 'ultimate';

      case FeatureType.MODULE:
        // Module access based on plan
        return ['starter', 'standard', 'pro', 'ultimate'].includes(planType || '');

      default:
        console.log('❌ Unknown feature type:', featureType);
        return false;
    }
  }

  /**
   * Get human-readable feature name
   * @param featureType Feature type
   * @returns Human-readable name
   */
  private getFeatureName(featureType: FeatureType): string {
    const featureNames = {
      [FeatureType.HEC_USER_DIARY]: 'HEC User Diary',
      [FeatureType.HEC_PLAY]: 'HEC Play',
      [FeatureType.ENGLISH_QA_WRITING]: 'English QA Writing',
      [FeatureType.ENGLISH_ESSAY]: 'English Essay',
      [FeatureType.ENGLISH_NOVEL]: 'English Novel',
      [FeatureType.MODULE]: 'Module'
    };

    return featureNames[featureType] || featureType;
  }
}
