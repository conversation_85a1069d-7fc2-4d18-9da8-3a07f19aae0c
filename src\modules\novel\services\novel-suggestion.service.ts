import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NovelSuggestion } from '../../../database/entities/novel-suggestion.entity';
import {
  CreateNovelSuggestionDto,
  NovelSuggestionResponseDto
} from '../../../database/models/novel.dto';

@Injectable()
export class NovelSuggestionService {
  private readonly logger = new Logger(NovelSuggestionService.name);

  constructor(
    @InjectRepository(NovelSuggestion)
    private readonly novelSuggestionRepository: Repository<NovelSuggestion>
  ) {}

  async createSuggestion(studentId: string, createSuggestionDto: CreateNovelSuggestionDto): Promise<NovelSuggestionResponseDto> {
    const suggestion = this.novelSuggestionRepository.create({
      ...createSuggestionDto,
      studentId
    });

    const savedSuggestion = await this.novelSuggestionRepository.save(suggestion);
    this.logger.log(`Student ${studentId} created novel suggestion: ${savedSuggestion.title}`);

    return this.mapSuggestionToResponseDto(savedSuggestion);
  }

  async getStudentSuggestions(studentId: string): Promise<NovelSuggestionResponseDto[]> {
    const suggestions = await this.novelSuggestionRepository.find({
      where: { studentId },
      order: { createdAt: 'DESC' }
    });

    return suggestions.map(suggestion => this.mapSuggestionToResponseDto(suggestion));
  }

  async getAllSuggestions(): Promise<NovelSuggestionResponseDto[]> {
    const suggestions = await this.novelSuggestionRepository.find({
      relations: ['student'],
      order: { createdAt: 'DESC' }
    });

    return suggestions.map(suggestion => this.mapSuggestionToResponseDto(suggestion));
  }

  private mapSuggestionToResponseDto(suggestion: NovelSuggestion): NovelSuggestionResponseDto {
    return {
      id: suggestion.id,
      title: suggestion.title,
      description: suggestion.description,
      studentId: suggestion.studentId,
      status: suggestion.status,
      createdAt: suggestion.createdAt,
      updatedAt: suggestion.updatedAt,
      studentName: suggestion.student?.name
    };
  }
}
