import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  ParseUUIDPipe
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiBody
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../common/guards/jwt.guard';
import { AdminGuard } from '../../../common/guards/admin.guard';
import { ApiResponse } from '../../../common/dto/api-response.dto';
import {
  ApiOkResponseWithType,
  ApiOkResponseWithArrayType,
  ApiErrorResponse
} from '../../../common/decorators/api-response.decorator';
import { NovelTopicService } from '../services/novel-topic.service';
import { NovelSuggestionService } from '../services/novel-suggestion.service';
import {
  CreateNovelTopicDto,
  UpdateNovelTopicDto,
  NovelTopicResponseDto,
  NovelSuggestionResponseDto,
  NovelCategoryResponseDto
} from '../../../database/models/novel.dto';
import { ApiOkResponseWithEmptyData } from 'src/common/decorators/api-empty-response.decorator';

@ApiTags('admin-novel')
@ApiBearerAuth('JWT-auth')
@Controller('admin/novel')
@UseGuards(JwtAuthGuard, AdminGuard)
export class AdminNovelController {
  constructor(
    private readonly novelTopicService: NovelTopicService,
    private readonly novelSuggestionService: NovelSuggestionService
  ) {}

  @Post('topics')
  @ApiOperation({ summary: 'Create a new novel topic' })
  @ApiBody({
    type: CreateNovelTopicDto,
    description: 'Novel topic creation data',
    examples: {
      example1: {
        value: {
          sequenceTitle: 'Topic 1',
          category: 'monthly',
          instruction: 'Write a creative story about friendship and adventure'
        }
      }
    }
  })
  @ApiOkResponseWithType(NovelTopicResponseDto, 'Novel topic created successfully')
  @ApiErrorResponse(400, 'Invalid input data')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(409, 'Conflict - Sequence title already exists')
  async createTopic(
    @Body() createTopicDto: CreateNovelTopicDto
  ): Promise<ApiResponse<NovelTopicResponseDto>> {
    const result = await this.novelTopicService.createTopic(createTopicDto);
    return ApiResponse.success(result, 'Novel topic created successfully', 201);
  }

  @Get('topics')
  @ApiOperation({ summary: 'Get all novel topics' })
  @ApiOkResponseWithArrayType(NovelTopicResponseDto, 'Retrieved all novel topics')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async getAllTopics(): Promise<ApiResponse<NovelTopicResponseDto[]>> {
    const result = await this.novelTopicService.getAllTopics();
    return ApiResponse.success(result, 'Retrieved all novel topics');
  }

  @Get('topics/:id')
  @ApiOperation({ summary: 'Get a specific novel topic by ID' })
  @ApiParam({ name: 'id', description: 'Topic ID', type: String })
  @ApiOkResponseWithType(NovelTopicResponseDto, 'Retrieved novel topic successfully')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Topic not found')
  async getTopicById(
    @Param('id', ParseUUIDPipe) id: string
  ): Promise<ApiResponse<NovelTopicResponseDto>> {
    const result = await this.novelTopicService.getTopicById(id);
    return ApiResponse.success(result, 'Retrieved novel topic successfully');
  }

  @Put('topics/:id')
  @ApiOperation({ summary: 'Update a novel topic' })
  @ApiParam({ name: 'id', description: 'Topic ID', type: String })
  @ApiBody({
    type: UpdateNovelTopicDto,
    description: 'Novel topic update data',
    examples: {
      example1: {
        value: {
          sequenceTitle: 'Topic 1 - Updated',
          category: 'quarterly',
          instruction: 'Write an updated creative story about friendship and adventure',
          isActive: true
        }
      }
    }
  })
  @ApiOkResponseWithType(NovelTopicResponseDto, 'Novel topic updated successfully')
  @ApiErrorResponse(400, 'Invalid input data')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Topic not found')
  @ApiErrorResponse(409, 'Conflict - Sequence title already exists')
  async updateTopic(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateTopicDto: UpdateNovelTopicDto
  ): Promise<ApiResponse<NovelTopicResponseDto>> {
    const result = await this.novelTopicService.updateTopic(id, updateTopicDto);
    return ApiResponse.success(result, 'Novel topic updated successfully');
  }

  @Delete('topics/:id')
  @ApiOperation({ summary: 'Delete a novel topic' })
  @ApiParam({ name: 'id', description: 'Topic ID', type: String })
  @ApiOkResponseWithEmptyData('Novel topic deleted successfully')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Topic not found')
  @ApiErrorResponse(409, 'Conflict - Cannot delete topic with entries')
  async deleteTopic(
    @Param('id', ParseUUIDPipe) id: string
  ): Promise<ApiResponse<void>> {
    await this.novelTopicService.deleteTopic(id);
    return ApiResponse.success(undefined, 'Novel topic deleted successfully');
  }

  @Get('suggestions')
  @ApiOperation({ summary: 'View all student novel suggestions' })
  @ApiOkResponseWithArrayType(NovelSuggestionResponseDto, 'Retrieved all novel suggestions')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async getAllSuggestions(): Promise<ApiResponse<NovelSuggestionResponseDto[]>> {
    const result = await this.novelSuggestionService.getAllSuggestions();
    return ApiResponse.success(result, 'Retrieved all novel suggestions');
  }

  @Get('categories')
  @ApiOperation({ summary: 'Get all available novel topic categories' })
  @ApiOkResponseWithArrayType(NovelCategoryResponseDto, 'Retrieved novel categories')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async getCategories(): Promise<ApiResponse<NovelCategoryResponseDto[]>> {
    const result = await this.novelTopicService.getCategories();
    return ApiResponse.success(result, 'Retrieved novel categories');
  }
}
