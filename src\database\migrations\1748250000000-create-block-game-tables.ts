import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateBlockGameTables1748250000000 implements MigrationInterface {
    name = 'CreateBlockGameTables1748250000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create block_game table
        await queryRunner.query(`
            CREATE TABLE "block_game" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP DEFAULT now(),
                "created_by" character varying(36),
                "updated_by" character varying(36),
                "title" character varying NOT NULL,
                "score" integer NOT NULL,
                "is_active" boolean NOT NULL DEFAULT true,
                CONSTRAINT "PK_block_game_id" PRIMARY KEY ("id")
            )
        `);

        // Create block_game_sentence table
        await queryRunner.query(`
            CREATE TABLE "block_game_sentence" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP DEFAULT now(),
                "created_by" character varying(36),
                "updated_by" character varying(36),
                "block_game_id" uuid NOT NULL,
                "starting_part" text NOT NULL,
                "expanding_part" text NOT NULL,
                "sentence_order" integer NOT NULL,
                CONSTRAINT "PK_block_game_sentence_id" PRIMARY KEY ("id")
            )
        `);

        // Create block_game_attempt table
        await queryRunner.query(`
            CREATE TABLE "block_game_attempt" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP DEFAULT now(),
                "student_id" uuid NOT NULL,
                "block_game_id" uuid NOT NULL,
                "score" real NOT NULL,
                "total_score" real NOT NULL,
                "sentence_constructions" jsonb NOT NULL,
                "submitted_at" TIMESTAMP NOT NULL,
                CONSTRAINT "PK_block_game_attempt_id" PRIMARY KEY ("id")
            )
        `);

        // Add foreign key constraints
        await queryRunner.query(`
            ALTER TABLE "block_game_sentence" 
            ADD CONSTRAINT "FK_block_game_sentence_block_game" 
            FOREIGN KEY ("block_game_id") REFERENCES "block_game"("id") ON DELETE CASCADE
        `);

        await queryRunner.query(`
            ALTER TABLE "block_game_attempt" 
            ADD CONSTRAINT "FK_block_game_attempt_student" 
            FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);

        await queryRunner.query(`
            ALTER TABLE "block_game_attempt" 
            ADD CONSTRAINT "FK_block_game_attempt_block_game" 
            FOREIGN KEY ("block_game_id") REFERENCES "block_game"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);

        // Add indexes for better performance
        await queryRunner.query(`CREATE INDEX "IDX_block_game_is_active" ON "block_game" ("is_active")`);
        await queryRunner.query(`CREATE INDEX "IDX_block_game_sentence_block_game_id" ON "block_game_sentence" ("block_game_id")`);
        await queryRunner.query(`CREATE INDEX "IDX_block_game_sentence_order" ON "block_game_sentence" ("block_game_id", "sentence_order")`);
        await queryRunner.query(`CREATE INDEX "IDX_block_game_attempt_student_id" ON "block_game_attempt" ("student_id")`);
        await queryRunner.query(`CREATE INDEX "IDX_block_game_attempt_block_game_id" ON "block_game_attempt" ("block_game_id")`);
        await queryRunner.query(`CREATE INDEX "IDX_block_game_attempt_submitted_at" ON "block_game_attempt" ("submitted_at")`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop indexes
        await queryRunner.query(`DROP INDEX "IDX_block_game_attempt_submitted_at"`);
        await queryRunner.query(`DROP INDEX "IDX_block_game_attempt_block_game_id"`);
        await queryRunner.query(`DROP INDEX "IDX_block_game_attempt_student_id"`);
        await queryRunner.query(`DROP INDEX "IDX_block_game_sentence_order"`);
        await queryRunner.query(`DROP INDEX "IDX_block_game_sentence_block_game_id"`);
        await queryRunner.query(`DROP INDEX "IDX_block_game_is_active"`);

        // Drop foreign key constraints
        await queryRunner.query(`ALTER TABLE "block_game_attempt" DROP CONSTRAINT "FK_block_game_attempt_block_game"`);
        await queryRunner.query(`ALTER TABLE "block_game_attempt" DROP CONSTRAINT "FK_block_game_attempt_student"`);
        await queryRunner.query(`ALTER TABLE "block_game_sentence" DROP CONSTRAINT "FK_block_game_sentence_block_game"`);

        // Drop tables
        await queryRunner.query(`DROP TABLE "block_game_attempt"`);
        await queryRunner.query(`DROP TABLE "block_game_sentence"`);
        await queryRunner.query(`DROP TABLE "block_game"`);
    }
}
