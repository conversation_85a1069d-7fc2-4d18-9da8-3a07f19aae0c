import { Injectable } from '@nestjs/common';
import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
  DeleteObjectCommand
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import {
  S3UploadOptions,
  S3UploadResult
} from '../interfaces/storage-provider.interface';
import { StorageConfigService } from '../services/storage-config.service';
import LoggerService from '../services/logger.service';

@Injectable()
export class S3StorageProvider {
  private s3Client: S3Client;
  private bucketName: string;
  private region: string;
  private cloudFrontDomain?: string;

  constructor(
    private readonly storageConfigService: StorageConfigService,
    private readonly logger: LoggerService
  ) {
    this.initializeS3Client();
  }

  private initializeS3Client(): void {
    try {
      const s3Config = this.storageConfigService.getS3Config();

      this.bucketName = s3Config.bucketName;
      this.region = s3Config.region;
      this.cloudFrontDomain = s3Config.cloudFrontDomain;

      this.s3Client = new S3Client({
        region: s3Config.region,
        credentials: {
          accessKeyId: s3Config.accessKeyId,
          secretAccessKey: s3Config.secretAccessKey
        }
      });

      this.logger.log(`S3 client initialized for bucket: ${this.bucketName} in region: ${this.region}`);
    } catch (error) {
      this.logger.error(`Failed to initialize S3 client: ${error.message}`);
      throw error;
    }
  }

  /**
   * Upload file to S3
   */
  async uploadFile(
    file: Express.Multer.File,
    key: string,
    options?: S3UploadOptions
  ): Promise<S3UploadResult> {
    try {
      const s3Config = this.storageConfigService.getS3Config();

      const uploadParams = {
        Bucket: this.bucketName,
        Key: key,
        Body: file.buffer,
        ContentType: file.mimetype || options?.contentType || 'application/octet-stream',
        ACL: s3Config.acl,
        StorageClass: s3Config.storageClass,
        ServerSideEncryption: s3Config.serverSideEncryption,
        Metadata: options?.metadata || {}
      };

      const command = new PutObjectCommand(uploadParams);
      const result = await this.s3Client.send(command);

      // Generate URL (CloudFront if available, otherwise S3)
      const url = this.generateFileUrl(key);

      this.logger.log(`S3 file uploaded: ${key} to bucket: ${this.bucketName}`);

      return {
        key,
        url,
        bucket: this.bucketName,
        region: this.region,
        etag: result.ETag?.replace(/"/g, ''), // Remove quotes from ETag
        versionId: result.VersionId,
        storageClass: uploadParams.StorageClass,
        serverSideEncryption: uploadParams.ServerSideEncryption,
        size: file.size,
        metadata: options?.metadata
      };

    } catch (error) {
      this.logger.error(`Failed to upload file to S3: ${error.message}`);
      throw new Error(`S3 upload failed: ${error.message}`);
    }
  }

  /**
   * Generate presigned URL for file access
   */
  async getPresignedUrl(key: string, expiresIn: number = 3600): Promise<string> {
    try {
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: key
      });

      const signedUrl = await getSignedUrl(this.s3Client, command, {
        expiresIn
      });

      return signedUrl;

    } catch (error) {
      this.logger.error(`Failed to generate presigned URL for S3: ${error.message}`);
      throw new Error(`S3 presigned URL generation failed: ${error.message}`);
    }
  }

  /**
   * Delete file from S3
   */
  async deleteFile(key: string): Promise<void> {
    try {
      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: key
      });

      await this.s3Client.send(command);
      this.logger.log(`S3 file deleted: ${key} from bucket: ${this.bucketName}`);

    } catch (error) {
      this.logger.error(`Failed to delete file from S3: ${error.message}`);
      throw new Error(`S3 delete failed: ${error.message}`);
    }
  }

  /**
   * Generate file URL (CloudFront if available, otherwise S3)
   */
  private generateFileUrl(key: string): string {
    if (this.cloudFrontDomain) {
      return `https://${this.cloudFrontDomain}/${key}`;
    }
    return `https://${this.bucketName}.s3.${this.region}.amazonaws.com/${key}`;
  }
}
