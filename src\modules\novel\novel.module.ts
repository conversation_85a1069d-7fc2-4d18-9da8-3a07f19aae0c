import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';

// Controllers
import { AdminNovelController } from './controllers/admin-novel.controller';
import { StudentNovelController } from './controllers/student-novel.controller';
import { TutorNovelController } from './controllers/tutor-novel.controller';

// Services
import { NovelTopicService } from './services/novel-topic.service';
import { NovelEntryService } from './services/novel-entry.service';
import { NovelReviewService } from './services/novel-review.service';
import { NovelSuggestionService } from './services/novel-suggestion.service';

// Entities
import { NovelTopic } from '../../database/entities/novel-topic.entity';
import { NovelEntry } from '../../database/entities/novel-entry.entity';
import { NovelFeedback } from '../../database/entities/novel-feedback.entity';
import { NovelCorrection } from '../../database/entities/novel-correction.entity';
import { NovelSuggestion } from '../../database/entities/novel-suggestion.entity';
import { NovelModuleSkinPreference } from '../../database/entities/novel-module-skin-preference.entity';

// External entities
import { User } from '../../database/entities/user.entity';
import { DiarySkin } from '../../database/entities/diary-skin.entity';
import { StudentDiarySkin } from '../../database/entities/student-diary-skin.entity';
import { DiarySkinRegistry } from '../../database/entities/diary-skin-registry.entity';
import { DiaryEntry } from '../../database/entities/diary-entry.entity';
import { Diary } from '../../database/entities/diary.entity';
import { StudentTutorMapping } from '../../database/entities/student-tutor-mapping.entity';

// External modules
import { CommonModule } from '../../common/common.module';
import { NotificationModule } from '../notification/notification.module';
import { AwardsModule } from '../awards/awards.module';
import { DiaryModule } from '../diary/diary.module';
import { AuthModule } from '../auth/auth.module';
import { TutorMatchingModule } from '../tutor-matching/tutor-matching.module';

// External services
import { DiarySkinService } from '../diary/diary-skin.service';
import { SubscriptionFeatureGuard } from '../../common/guards/subscription-feature.guard';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      // Novel entities
      NovelTopic,
      NovelEntry,
      NovelFeedback,
      NovelCorrection,
      NovelSuggestion,
      NovelModuleSkinPreference,
      // External entities
      User,
      DiarySkin,
      StudentDiarySkin,
      DiarySkinRegistry,
      DiaryEntry,
      Diary,
      StudentTutorMapping
    ]),
    CommonModule,
    forwardRef(() => NotificationModule),
    forwardRef(() => AwardsModule),
    forwardRef(() => DiaryModule),
    forwardRef(() => AuthModule),
    forwardRef(() => TutorMatchingModule)
  ],
  controllers: [
    AdminNovelController,
    StudentNovelController,
    TutorNovelController
  ],
  providers: [
    NovelTopicService,
    NovelEntryService,
    NovelReviewService,
    NovelSuggestionService,
    DiarySkinService,
    SubscriptionFeatureGuard,
    JwtService
  ],
  exports: [
    NovelTopicService,
    NovelEntryService,
    NovelReviewService,
    NovelSuggestionService
  ]
})
export class NovelModule {}
